#!/usr/bin/env python3
"""
Direct test of Vertex AI connection without LiteLLM
"""

import os
import sys

def test_vertex_ai_direct():
    """Test Vertex AI connection directly"""
    print("\n🔧 Testing Direct Vertex AI Connection")
    print("=" * 45)
    
    try:
        import vertexai
        from vertexai.generative_models import GenerativeModel
        print("✅ Vertex AI SDK imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Vertex AI SDK: {e}")
        print("Install with: pip install google-cloud-aiplatform")
        return False
    
    # Initialize Vertex AI
    project_id = "truxtsaas"
    location = "us-central1"
    
    try:
        vertexai.init(project=project_id, location=location)
        print(f"✅ Vertex AI initialized for project: {project_id}")
        print(f"✅ Location: {location}")
    except Exception as e:
        print(f"❌ Failed to initialize Vertex AI: {e}")
        return False
    
    # Test with Gemini 1.5 Pro
    try:
        print("\n🧪 Testing Gemini 1.5 Pro...")
        model = GenerativeModel("gemini-1.5-pro")
        response = model.generate_content("Hello! Please respond with just 'OK' to confirm you're working.")
        print(f"✅ Model response: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Failed to call Gemini 1.5 Pro: {e}")
        
        # Try Gemini 1.5 Flash as fallback
        try:
            print("\n🧪 Trying Gemini 1.5 Flash as fallback...")
            model = GenerativeModel("gemini-1.5-flash")
            response = model.generate_content("Hello! Please respond with just 'OK' to confirm you're working.")
            print(f"✅ Model response: {response.text}")
            return True
        except Exception as e2:
            print(f"❌ Failed to call Gemini 1.5 Flash: {e2}")
            
            # Try basic Gemini Pro
            try:
                print("\n🧪 Trying basic Gemini Pro as fallback...")
                model = GenerativeModel("gemini-pro")
                response = model.generate_content("Hello! Please respond with just 'OK' to confirm you're working.")
                print(f"✅ Model response: {response.text}")
                return True
            except Exception as e3:
                print(f"❌ Failed to call Gemini Pro: {e3}")
                return False

def test_litellm_vertex():
    """Test LiteLLM with Vertex AI"""
    print("\n🔧 Testing LiteLLM with Vertex AI")
    print("=" * 35)
    
    try:
        import litellm
        print("✅ LiteLLM imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import LiteLLM: {e}")
        return False
    
    # Set Vertex AI parameters
    litellm.vertex_project = "truxtsaas"
    litellm.vertex_location = "us-central1"
    
    # Test different model names
    models_to_test = [
        "vertex_ai/gemini-pro",
        "vertex_ai/gemini-1.5-pro",
        "vertex_ai/gemini-1.5-flash",
    ]
    
    for model in models_to_test:
        try:
            print(f"\n🧪 Testing {model}...")
            response = litellm.completion(
                model=model,
                messages=[{"role": "user", "content": "Hello! Please respond with just 'OK' to confirm you're working."}],
                max_tokens=10
            )
            print(f"✅ {model} response: {response.choices[0].message.content}")
            return True
        except Exception as e:
            print(f"❌ {model} failed: {e}")
    
    return False

if __name__ == "__main__":
    print("🦌 DeerFlow Vertex AI Direct Connection Test")
    print("=" * 50)
    
    # Test direct Vertex AI connection
    direct_success = test_vertex_ai_direct()
    
    # Test LiteLLM connection
    litellm_success = test_litellm_vertex()
    
    if direct_success:
        print("\n🎉 Direct Vertex AI connection successful!")
        if litellm_success:
            print("🎉 LiteLLM integration also working!")
        else:
            print("⚠️  LiteLLM integration needs configuration adjustment")
    else:
        print("\n❌ Direct Vertex AI connection failed")
        print("Please check:")
        print("1. Google Cloud authentication: gcloud auth application-default login")
        print("2. Project access and permissions")
        print("3. Vertex AI API enabled")
    
    sys.exit(0 if (direct_success or litellm_success) else 1)
