#!/usr/bin/env python3
"""
Test script to verify Google Vertex AI integration with DeerFlow
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from src.llms.llm import get_llm_by_type
    from src.config import load_yaml_config
    print("✅ Successfully imported DeerFlow modules")
except ImportError as e:
    print(f"❌ Failed to import DeerFlow modules: {e}")
    print("Make sure you're running this from the DeerFlow root directory")
    sys.exit(1)

def test_vertex_ai_config():
    """Test if Vertex AI is properly configured"""
    print("\n🦌 Testing DeerFlow Vertex AI Configuration")
    print("=" * 50)
    
    # Check if conf.yaml exists
    conf_path = Path(__file__).parent.parent / "conf.yaml"
    if not conf_path.exists():
        print("❌ conf.yaml not found. Please copy conf.yaml.example to conf.yaml")
        return False
    
    print("✅ conf.yaml found")
    
    # Load configuration
    try:
        conf = load_yaml_config(str(conf_path))
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    # Check BASIC_MODEL configuration
    basic_model_conf = conf.get("BASIC_MODEL", {})
    if not basic_model_conf:
        print("❌ BASIC_MODEL not configured in conf.yaml")
        return False
    
    model_name = basic_model_conf.get("model", "")
    print(f"📝 Model configured: {model_name}")
    
    # Check if it's a Vertex AI model
    if model_name.startswith("vertex_ai/"):
        print("✅ Vertex AI model detected")

        # Check if it's using the latest Gemini 2.5 models
        if "gemini-2.5" in model_name:
            print("🚀 Using latest Gemini 2.5 model!")
        elif "gemini-1.5" in model_name:
            print("ℹ️  Using Gemini 1.5 model (consider upgrading to 2.5)")

        # Check required Vertex AI parameters (can be in model config or litellm_settings)
        vertex_project = basic_model_conf.get("vertex_project") or conf.get("litellm_settings", {}).get("vertex_project")
        vertex_location = basic_model_conf.get("vertex_location") or conf.get("litellm_settings", {}).get("vertex_location")

        if not vertex_project:
            print("❌ vertex_project not configured in model config or litellm_settings")
            return False

        if not vertex_location:
            print("❌ vertex_location not configured in model config or litellm_settings")
            return False

        print(f"✅ Vertex AI project: {vertex_project}")
        print(f"✅ Vertex AI location: {vertex_location}")
        
        # Test authentication
        try:
            from google.auth import default
            credentials, project = default()
            print(f"✅ Google Cloud authentication successful")
            print(f"✅ Authenticated project: {project}")
            
            if project != vertex_project:
                print(f"⚠️  Warning: Authenticated project ({project}) differs from configured project ({vertex_project})")
        
        except Exception as e:
            print(f"❌ Google Cloud authentication failed: {e}")
            print("Please run: gcloud auth application-default login")
            return False
        
    else:
        print("ℹ️  Not using Vertex AI model (this is fine if intentional)")
        return True
    
    # Test LLM creation
    try:
        print("\n🧪 Testing LLM creation...")
        llm = get_llm_by_type("basic")
        print("✅ LLM instance created successfully")
        
        # Test a simple call
        print("🧪 Testing model call...")
        response = llm.invoke("Hello! Please respond with just 'OK' to confirm you're working.")
        print(f"✅ Model response: {response.content}")
        
    except Exception as e:
        print(f"❌ Failed to create or test LLM: {e}")
        return False
    
    print("\n🎉 All tests passed! Vertex AI is properly configured.")
    return True

if __name__ == "__main__":
    success = test_vertex_ai_config()
    sys.exit(0 if success else 1)
