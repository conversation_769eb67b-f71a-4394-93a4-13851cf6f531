#!/bin/bash

# Google Vertex AI Setup Script for DeerFlow
# This script helps you set up Google Cloud authentication for Vertex AI

set -e

echo "🦌 DeerFlow - Google Vertex AI Setup"
echo "===================================="
echo ""

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK is not installed."
    echo ""
    echo "Please install it first:"
    echo "  macOS: brew install google-cloud-sdk"
    echo "  Other: https://cloud.google.com/sdk/docs/install"
    echo ""
    exit 1
fi

echo "✅ Google Cloud SDK is installed"

# Get project ID
echo ""
read -p "Enter your Google Cloud Project ID: " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Project ID cannot be empty"
    exit 1
fi

echo ""
echo "Setting up authentication for project: $PROJECT_ID"
echo ""

# Set the project
echo "📝 Setting default project..."
gcloud config set project "$PROJECT_ID"

# Login
echo "🔐 Logging in to Google Cloud..."
gcloud auth login

# Set application default credentials
echo "🔑 Setting up application default credentials..."
gcloud auth application-default login

# Enable Vertex AI API
echo "🚀 Enabling Vertex AI API..."
gcloud services enable aiplatform.googleapis.com

# Get the region
echo ""
echo "Available regions for Vertex AI:"
echo "  - us-central1 (Iowa)"
echo "  - us-east1 (South Carolina)"
echo "  - us-west1 (Oregon)"
echo "  - europe-west1 (Belgium)"
echo "  - asia-southeast1 (Singapore)"
echo ""
read -p "Enter your preferred region [us-central1]: " REGION
REGION=${REGION:-us-central1}

echo ""
echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Copy conf.yaml.example to conf.yaml:"
echo "   cp conf.yaml.example conf.yaml"
echo ""
echo "2. Update your conf.yaml with:"
echo "   BASIC_MODEL:"
echo "     model: \"vertex_ai/gemini-1.5-pro\""
echo "     vertex_project: \"$PROJECT_ID\""
echo "     vertex_location: \"$REGION\""
echo ""
echo "3. Optionally, add to your .env file:"
echo "   GOOGLE_CLOUD_PROJECT=\"$PROJECT_ID\""
echo "   GOOGLE_CLOUD_REGION=\"$REGION\""
echo ""
echo "🎉 You're ready to use Google Vertex AI with DeerFlow!"
