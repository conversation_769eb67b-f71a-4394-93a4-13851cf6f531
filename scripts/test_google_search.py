#!/usr/bin/env python3
"""
Test script to verify Google Search integration with DeerFlow
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_google_search_setup():
    """Test if Google Search is properly configured"""
    print("\n🔍 Testing DeerFlow Google Search Configuration")
    print("=" * 55)
    
    # Test environment variables
    search_api = os.getenv("SEARCH_API", "")
    print(f"📝 Current search engine: {search_api}")
    
    if search_api == "google_search":
        api_key = os.getenv("GOOGLE_SEARCH_API_KEY")
        engine_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        
        if not api_key:
            print("❌ GOOGLE_SEARCH_API_KEY not set")
            return False
        if not engine_id:
            print("❌ GOOGLE_SEARCH_ENGINE_ID not set")
            return False
            
        print("✅ Google Custom Search credentials configured")
        
    elif search_api == "google_ai_search":
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        engine_id = os.getenv("GOOGLE_AI_SEARCH_ENGINE_ID")
        
        if not project_id:
            print("❌ GOOGLE_CLOUD_PROJECT not set")
            return False
        if not engine_id:
            print("❌ GOOGLE_AI_SEARCH_ENGINE_ID not set")
            print("ℹ️  Note: You can still test without this - it will fallback to Google Custom Search")
            
        print(f"✅ Google Cloud project: {project_id}")
        
        # Test Google Cloud authentication
        try:
            from google.auth import default
            credentials, project = default()
            print(f"✅ Google Cloud authentication successful")
            print(f"✅ Authenticated project: {project}")
            
            if project != project_id:
                print(f"⚠️  Warning: Authenticated project ({project}) differs from configured project ({project_id})")
        
        except Exception as e:
            print(f"❌ Google Cloud authentication failed: {e}")
            print("Please run: gcloud auth application-default login")
            return False
    
    # Test search tool import
    try:
        from src.tools.search import get_web_search_tool
        print("✅ Search tools imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import search tools: {e}")
        return False
    
    # Test search tool creation
    try:
        print("\n🧪 Testing search tool creation...")
        search_tool = get_web_search_tool(max_search_results=3)
        print(f"✅ Search tool created: {search_tool.name}")
        
        # Test a simple search
        print("🧪 Testing search functionality...")
        if search_api in ["google_search", "google_ai_search"]:
            print("ℹ️  Skipping actual search test for Google services (requires valid credentials)")
            print("ℹ️  To test search functionality, run DeerFlow with a real query")
        else:
            # Test with other search engines
            try:
                results = search_tool.invoke("test query")
                print(f"✅ Search test successful, got {len(results) if isinstance(results, list) else 'some'} results")
            except Exception as e:
                print(f"⚠️  Search test failed: {e}")
                print("ℹ️  This might be expected if credentials are not configured")
        
    except Exception as e:
        print(f"❌ Failed to create or test search tool: {e}")
        return False
    
    print("\n🎉 Google Search integration setup complete!")
    print("\nNext steps:")
    if search_api == "google_search":
        print("1. Set up Google Custom Search:")
        print("   - Get API key from Google Cloud Console")
        print("   - Create Custom Search Engine at https://cse.google.com/")
        print("   - Set GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_ENGINE_ID in .env")
    elif search_api == "google_ai_search":
        print("1. Set up Google AI Search:")
        print("   - Enable Discovery Engine API: gcloud services enable discoveryengine.googleapis.com")
        print("   - Create search app in Google Cloud Console")
        print("   - Set GOOGLE_AI_SEARCH_ENGINE_ID in .env")
    
    print("2. Test with DeerFlow:")
    print("   uv run main.py 'What is artificial intelligence?'")
    
    return True

if __name__ == "__main__":
    success = test_google_search_setup()
    sys.exit(0 if success else 1)
