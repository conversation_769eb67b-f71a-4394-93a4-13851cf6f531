# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import logging
from typing import Any, Dict, List, Optional, Type

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

try:
    from googleapiclient.discovery import build
    from google.auth import default
    GOOGLE_SEARCH_AVAILABLE = True
except ImportError:
    GOOGLE_SEARCH_AVAILABLE = False
    logger.warning("Google API client not available. Install with: pip install google-api-python-client google-auth")


class GoogleSearchInput(BaseModel):
    """Input for Google Custom Search."""
    query: str = Field(description="The search query")


class GoogleCustomSearch(BaseTool):
    """Tool that queries Google Custom Search API and returns results."""
    
    name: str = "google_search"
    description: str = (
        "A wrapper around Google Custom Search. "
        "Useful for when you need to answer questions about current events. "
        "Input should be a search query."
    )
    args_schema: Type[BaseModel] = GoogleSearchInput
    
    api_key: Optional[str] = None
    search_engine_id: Optional[str] = None
    max_results: int = 10
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not GOOGLE_SEARCH_AVAILABLE:
            raise ImportError(
                "Google API client not available. "
                "Install with: pip install google-api-python-client google-auth"
            )
        
        # Get API key and search engine ID from environment if not provided
        if not self.api_key:
            self.api_key = os.getenv("GOOGLE_SEARCH_API_KEY")
        if not self.search_engine_id:
            self.search_engine_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
            
        if not self.api_key:
            raise ValueError(
                "Google Search API key not found. "
                "Set GOOGLE_SEARCH_API_KEY environment variable or pass api_key parameter."
            )
        if not self.search_engine_id:
            raise ValueError(
                "Google Search Engine ID not found. "
                "Set GOOGLE_SEARCH_ENGINE_ID environment variable or pass search_engine_id parameter."
            )

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> List[Dict[str, Any]]:
        """Use the tool."""
        try:
            service = build("customsearch", "v1", developerKey=self.api_key)
            
            result = service.cse().list(
                q=query,
                cx=self.search_engine_id,
                num=min(self.max_results, 10)  # Google API max is 10 per request
            ).execute()
            
            search_results = []
            if 'items' in result:
                for item in result['items']:
                    search_result = {
                        'title': item.get('title', ''),
                        'link': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'content': item.get('snippet', '')  # For compatibility with other search tools
                    }
                    search_results.append(search_result)
            
            logger.info(f"Google Search returned {len(search_results)} results for query: {query}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in Google Search: {e}")
            return [{"error": f"Google Search failed: {str(e)}"}]

    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> List[Dict[str, Any]]:
        """Use the tool asynchronously."""
        # For now, just call the sync version
        # In a production environment, you might want to use aiohttp or similar
        return self._run(query, run_manager)


class GoogleAISearch(BaseTool):
    """Tool that uses Google AI Search (Vertex AI Search) for enhanced results."""
    
    name: str = "google_ai_search"
    description: str = (
        "A wrapper around Google AI Search (Vertex AI Search). "
        "Provides AI-enhanced search results with better understanding of context. "
        "Input should be a search query."
    )
    args_schema: Type[BaseModel] = GoogleSearchInput
    
    project_id: Optional[str] = None
    location: str = "global"
    search_engine_id: Optional[str] = None
    max_results: int = 10
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Get project ID from environment if not provided
        if not self.project_id:
            self.project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not self.search_engine_id:
            self.search_engine_id = os.getenv("GOOGLE_AI_SEARCH_ENGINE_ID")
            
        if not self.project_id:
            raise ValueError(
                "Google Cloud Project ID not found. "
                "Set GOOGLE_CLOUD_PROJECT environment variable or pass project_id parameter."
            )

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> List[Dict[str, Any]]:
        """Use the tool."""
        try:
            # Import here to avoid dependency issues if not available
            from google.cloud import discoveryengine_v1 as discoveryengine
            
            # Create a client
            client = discoveryengine.SearchServiceClient()
            
            # The full resource name of the search app serving config
            serving_config = f"projects/{self.project_id}/locations/{self.location}/collections/default_collection/engines/{self.search_engine_id}/servingConfigs/default_config"
            
            # Optional: Configuration options for search
            # Refer to the `ContentSearchSpec` reference for all supported fields
            content_search_spec = discoveryengine.SearchRequest.ContentSearchSpec(
                # For information about snippets, refer to:
                # https://cloud.google.com/generative-ai-app-builder/docs/snippets
                snippet_spec=discoveryengine.SearchRequest.ContentSearchSpec.SnippetSpec(
                    return_snippet=True
                ),
                # For information about search summaries, refer to:
                # https://cloud.google.com/generative-ai-app-builder/docs/get-search-summaries
                summary_spec=discoveryengine.SearchRequest.ContentSearchSpec.SummarySpec(
                    summary_result_count=5,
                    include_citations=True,
                ),
            )

            # Refer to the `SearchRequest` reference for all supported fields
            request = discoveryengine.SearchRequest(
                serving_config=serving_config,
                query=query,
                page_size=self.max_results,
                content_search_spec=content_search_spec,
                query_expansion_spec=discoveryengine.SearchRequest.QueryExpansionSpec(
                    condition=discoveryengine.SearchRequest.QueryExpansionSpec.Condition.AUTO,
                ),
                spell_correction_spec=discoveryengine.SearchRequest.SpellCorrectionSpec(
                    mode=discoveryengine.SearchRequest.SpellCorrectionSpec.Mode.AUTO
                ),
            )

            response = client.search(request)
            
            search_results = []
            for result in response.results:
                document = result.document
                search_result = {
                    'title': document.derived_struct_data.get('title', ''),
                    'link': document.derived_struct_data.get('link', ''),
                    'snippet': document.derived_struct_data.get('snippet', ''),
                    'content': document.derived_struct_data.get('snippet', '')
                }
                search_results.append(search_result)
            
            # Add summary if available
            if hasattr(response, 'summary') and response.summary:
                search_results.insert(0, {
                    'title': 'AI Summary',
                    'content': response.summary.summary_text,
                    'type': 'summary'
                })
            
            logger.info(f"Google AI Search returned {len(search_results)} results for query: {query}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in Google AI Search: {e}")
            # Fallback to regular Google search if AI search fails
            logger.info("Falling back to Google Custom Search")
            fallback_search = GoogleCustomSearch(max_results=self.max_results)
            return fallback_search._run(query, run_manager)

    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> List[Dict[str, Any]]:
        """Use the tool asynchronously."""
        return self._run(query, run_manager)
