# [!NOTE]
# Read the `docs/configuration_guide.md` carefully, and update the configurations to match your specific settings and requirements.
# - Replace `api_key` with your own credentials
# - Replace `base_url` and `model` name if you want to use a custom model

# Example: Doubao model (default)
BASIC_MODEL:
  base_url: https://ark.cn-beijing.volces.com/api/v3
  model: "doubao-1-5-pro-32k-250115"
  api_key: xxxx

# Example: Google Vertex AI Gemini 2.5 models (Latest)
BASIC_MODEL:
  model: "vertex_ai/gemini-2.5-flash"

# Alternative Gemini 2.5 models:
# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.5-pro"  # Most capable model

# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.5-flash-preview-05-20"  # Latest preview

# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.5-pro-preview-05-06"  # Latest Pro preview

# Global Vertex AI settings (applies to all vertex_ai models)
litellm_settings:
  vertex_project: "your-project-id"
  vertex_location: "us-central1"
