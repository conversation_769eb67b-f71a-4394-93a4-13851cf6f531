# Configuration Guide

## Quick Settings

Copy the `conf.yaml.example` file to `conf.yaml` and modify the configurations to match your specific settings and requirements.

```bash
cd deer-flow
cp conf.yaml.example conf.yaml
```

## Which models does DeerFlow support?

In DeerFlow, currently we only support non-reasoning models, which means models like OpenAI's o1/o3 or DeepSeek's R1 are not supported yet, but we will add support for them in the future.

### Supported Models

`doubao-1.5-pro-32k-250115`, `gpt-4o`, `qwen-max-latest`, `gemini-2.0-flash`, `deepseek-v3`, and theoretically any other non-reasoning chat models that implement the OpenAI API specification.

> [!NOTE]
> The Deep Research process requires the model to have a **longer context window**, which is not supported by all models.
> A work-around is to set the `Max steps of a research plan` to `2` in the settings dialog located on the top right corner of the web page,
> or set `max_step_num` to `2` when invoking the API.

### How to switch models?
You can switch the model in use by modifying the `conf.yaml` file in the root directory of the project, using the configuration in the [litellm format](https://docs.litellm.ai/docs/providers/openai_compatible).

---

### How to use OpenAI-Compatible models?

DeerFlow supports integration with OpenAI-Compatible models, which are models that implement the OpenAI API specification. This includes various open-source and commercial models that provide API endpoints compatible with the OpenAI format. You can refer to [litellm OpenAI-Compatible](https://docs.litellm.ai/docs/providers/openai_compatible) for detailed documentation.
The following is a configuration example of `conf.yaml` for using OpenAI-Compatible models:

```yaml
# An example of Doubao models served by VolcEngine
BASIC_MODEL:
  base_url: "https://ark.cn-beijing.volces.com/api/v3"
  model: "doubao-1.5-pro-32k-250115"
  api_key: YOUR_API_KEY

# An example of Aliyun models
BASIC_MODEL:
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  model: "qwen-max-latest"
  api_key: YOUR_API_KEY

# An example of deepseek official models
BASIC_MODEL:
  base_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  api_key: YOU_API_KEY

# An example of Google Gemini models using OpenAI-Compatible interface
BASIC_MODEL:
  base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
  model: "gemini-2.0-flash"
  api_key: YOUR_API_KEY
```

### How to use Ollama models?

DeerFlow supports the integration of Ollama models. You can refer to [litellm Ollama](https://docs.litellm.ai/docs/providers/ollama). <br>
The following is a configuration example of `conf.yaml` for using Ollama models:

```yaml
BASIC_MODEL:
  model: "ollama/ollama-model-name"
  base_url: "http://localhost:11434" # Local service address of Ollama, which can be started/viewed via ollama serve
```

### How to use OpenRouter models?

DeerFlow supports the integration of OpenRouter models. You can refer to [litellm OpenRouter](https://docs.litellm.ai/docs/providers/openrouter). To use OpenRouter models, you need to:
1. Obtain the OPENROUTER_API_KEY from OpenRouter (https://openrouter.ai/) and set it in the environment variable.
2. Add the `openrouter/` prefix before the model name.
3. Configure the correct OpenRouter base URL.

The following is a configuration example for using OpenRouter models:
1. Configure OPENROUTER_API_KEY in the environment variable (such as the `.env` file)
```ini
OPENROUTER_API_KEY=""
```
2. Set the model name in `conf.yaml`
```yaml
BASIC_MODEL:
  model: "openrouter/google/palm-2-chat-bison"
```

Note: The available models and their exact names may change over time. Please verify the currently available models and their correct identifiers in [OpenRouter's official documentation](https://openrouter.ai/docs).

### How to use Azure models?

DeerFlow supports the integration of Azure models. You can refer to [litellm Azure](https://docs.litellm.ai/docs/providers/azure). Configuration example of `conf.yaml`:
```yaml
BASIC_MODEL:
  model: "azure/gpt-4o-2024-08-06"
  api_base: $AZURE_API_BASE
  api_version: $AZURE_API_VERSION
  api_key: $AZURE_API_KEY
```

### How to use Google Vertex AI models?

DeerFlow supports the integration of Google Vertex AI models. You can refer to [litellm Vertex AI](https://docs.litellm.ai/docs/providers/vertex) for detailed documentation.

#### Prerequisites

1. **Install Google Cloud SDK** (if not already installed):
   ```bash
   # macOS
   brew install google-cloud-sdk

   # Or download from: https://cloud.google.com/sdk/docs/install
   ```

2. **Set up authentication** using one of these methods:

   **Method 1: Google Cloud CLI (Recommended for development)**
   ```bash
   # Login to your Google account
   gcloud auth login

   # Set your project
   gcloud config set project YOUR_PROJECT_ID

   # Set application default credentials
   gcloud auth application-default login
   ```

   **Method 2: Service Account Key (Recommended for production)**
   ```bash
   # Create a service account and download the key file
   # Then set the environment variable
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   ```

3. **Enable the Vertex AI API** in your Google Cloud project:
   ```bash
   gcloud services enable aiplatform.googleapis.com
   ```

#### Configuration

Add the following configuration to your `conf.yaml` file:

```yaml
# Example: Google Vertex AI Gemini model
BASIC_MODEL:
  model: "vertex_ai/gemini-1.5-pro"
  vertex_project: "your-project-id"
  vertex_location: "us-central1"  # or your preferred region

# Alternative: Using Gemini 2.0 Flash
# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.0-flash-exp"
#   vertex_project: "your-project-id"
#   vertex_location: "us-central1"
```

#### Environment Variables (Optional)

You can also set these environment variables in your `.env` file:

```ini
GOOGLE_CLOUD_PROJECT="your-project-id"
GOOGLE_CLOUD_REGION="us-central1"
GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
```

#### Available Models

Popular Vertex AI models you can use:
- `vertex_ai/gemini-1.5-pro` - Latest Gemini Pro model
- `vertex_ai/gemini-1.5-flash` - Faster Gemini model
- `vertex_ai/gemini-2.0-flash-exp` - Experimental Gemini 2.0
- `vertex_ai/claude-3-5-sonnet@********` - Anthropic Claude via Vertex AI
- `vertex_ai/claude-3-5-haiku@********` - Anthropic Claude Haiku via Vertex AI

Note: Model availability may vary by region. Check the [Vertex AI documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models) for the latest model list.

## Search Engine Configuration

DeerFlow supports multiple search engines that can be configured in your `.env` file using the `SEARCH_API` variable:

### Available Search Engines

- **Tavily** (default): A specialized search API for AI applications
  - Requires `TAVILY_API_KEY` in your `.env` file
  - Sign up at: https://app.tavily.com/home

- **DuckDuckGo**: Privacy-focused search engine
  - No API key required

- **Brave Search**: Privacy-focused search engine with advanced features
  - Requires `BRAVE_SEARCH_API_KEY` in your `.env` file
  - Sign up at: https://brave.com/search/api/

- **Arxiv**: Scientific paper search for academic research
  - No API key required
  - Specialized for scientific and academic papers

- **Google Custom Search**: Google's programmable search engine
  - Requires `GOOGLE_SEARCH_API_KEY` and `GOOGLE_SEARCH_ENGINE_ID`
  - Setup instructions below

- **Google AI Search**: Enhanced search using Google's Vertex AI Search
  - Requires Google Cloud project with Vertex AI Search enabled
  - Uses Google Cloud authentication (same as Vertex AI models)

### How to set up Google Custom Search

1. **Create a Google Cloud Project** (if you don't have one):
   ```bash
   gcloud projects create your-project-id
   gcloud config set project your-project-id
   ```

2. **Enable the Custom Search API**:
   ```bash
   gcloud services enable customsearch.googleapis.com
   ```

3. **Create an API Key**:
   - Go to the [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
   - Click "Create Credentials" → "API Key"
   - Copy the API key

4. **Create a Custom Search Engine**:
   - Go to [Google Custom Search](https://cse.google.com/cse/)
   - Click "Add" to create a new search engine
   - Enter `*.com` as the site to search (or specify specific sites)
   - Click "Create"
   - Copy the Search Engine ID from the setup page

5. **Configure your `.env` file**:
   ```ini
   SEARCH_API=google_search
   GOOGLE_SEARCH_API_KEY=your-api-key
   GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id
   ```

### How to set up Google AI Search (Vertex AI Search)

1. **Enable Vertex AI Search API**:
   ```bash
   gcloud services enable discoveryengine.googleapis.com
   ```

2. **Create a Search App** in the [Google Cloud Console](https://console.cloud.google.com/gen-app-builder/engines):
   - Go to Agent Builder → Search
   - Create a new search app
   - Choose "Website" as data source or upload your documents
   - Copy the Search Engine ID

3. **Configure your `.env` file**:
   ```ini
   SEARCH_API=google_ai_search
   GOOGLE_AI_SEARCH_ENGINE_ID=your-search-engine-id
   GOOGLE_CLOUD_PROJECT=your-project-id
   ```

Note: Google AI Search uses the same Google Cloud authentication as Vertex AI models, so if you've already set up Vertex AI, no additional authentication is needed.
