# 🚀 Gemini 2.5 Setup Guide for DeerFlow

This guide will help you set up DeerFlow with the latest Google Gemini 2.5 models using Vertex AI and Google authentication credentials.

## 🎯 Overview

Gemini 2.5 represents Google's latest and most advanced AI models, offering:
- **Gemini 2.5 Flash**: Fast, efficient model perfect for most research tasks
- **Gemini 2.5 Pro**: Most capable model for complex analysis and reasoning

## 📋 Prerequisites

1. **Google Cloud Account** with billing enabled
2. **Google Cloud SDK** installed
3. **DeerFlow** repository cloned and dependencies installed

## 🛠️ Step-by-Step Setup

### 1. Install Google Cloud SDK

```bash
# macOS
brew install google-cloud-sdk

# Or download from: https://cloud.google.com/sdk/docs/install
```

### 2. Set Up Google Cloud Project

```bash
# Create a new project (optional)
gcloud projects create your-project-id

# Set your project
gcloud config set project your-project-id

# Enable required APIs
gcloud services enable aiplatform.googleapis.com
gcloud services enable discoveryengine.googleapis.com  # For Google AI Search
```

### 3. Authenticate with Google Cloud

```bash
# Login to your Google account
gcloud auth login

# Set application default credentials (required for Vertex AI)
gcloud auth application-default login
```

### 4. Configure DeerFlow

#### Update `conf.yaml`:

```yaml
# Gemini 2.5 Flash (Recommended for most use cases)
BASIC_MODEL:
  model: "vertex_ai/gemini-2.5-flash"
  vertex_project: "your-actual-project-id"
  vertex_location: "us-central1"

# Alternative: Gemini 2.5 Pro (Most capable)
# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.5-pro"
#   vertex_project: "your-actual-project-id"
#   vertex_location: "us-central1"

# Alternative: Latest preview versions
# BASIC_MODEL:
#   model: "vertex_ai/gemini-2.5-flash-preview-05-20"
#   vertex_project: "your-actual-project-id"
#   vertex_location: "us-central1"
```

#### Update `.env` (Optional):

```ini
# Google Cloud configuration
GOOGLE_CLOUD_PROJECT="your-actual-project-id"
GOOGLE_CLOUD_REGION="us-central1"

# Search engine (use Google AI Search for best results)
SEARCH_API=google_ai_search
GOOGLE_AI_SEARCH_ENGINE_ID="your-search-engine-id"  # Optional
```

### 5. Test Your Setup

```bash
# Test Vertex AI configuration
uv run python scripts/test_vertex_ai.py

# Test Google Search integration
uv run python scripts/test_google_search.py

# Test the full system
uv run main.py "What are the latest developments in AI?"
```

## 🌍 Available Regions

Choose the region closest to you for better performance:

- **us-central1** (Iowa) - Default, good global performance
- **us-east1** (South Carolina)
- **us-west1** (Oregon)
- **europe-west1** (Belgium)
- **asia-southeast1** (Singapore)

## 🔧 Model Comparison

| Model | Speed | Capability | Use Case |
|-------|-------|------------|----------|
| `gemini-2.5-flash` | ⚡ Fast | 🎯 High | General research, quick analysis |
| `gemini-2.5-pro` | 🐌 Slower | 🚀 Highest | Complex reasoning, detailed analysis |
| `gemini-2.5-flash-preview-05-20` | ⚡ Fast | 🎯 High | Latest features, experimental |
| `gemini-2.5-pro-preview-05-06` | 🐌 Slower | 🚀 Highest | Latest Pro features, experimental |

## 🔍 Google AI Search Setup (Optional)

For enhanced search capabilities:

1. **Enable Discovery Engine API**:
   ```bash
   gcloud services enable discoveryengine.googleapis.com
   ```

2. **Create a Search App**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/gen-app-builder/engines)
   - Navigate to Agent Builder → Search
   - Create a new search app
   - Choose "Website" as data source
   - Copy the Search Engine ID

3. **Update your `.env`**:
   ```ini
   SEARCH_API=google_ai_search
   GOOGLE_AI_SEARCH_ENGINE_ID="your-search-engine-id"
   ```

## 🚨 Troubleshooting

### Authentication Issues

```bash
# Re-authenticate if you get auth errors
gcloud auth application-default login

# Check current authentication
gcloud auth list
```

### API Not Enabled

```bash
# Enable all required APIs
gcloud services enable aiplatform.googleapis.com
gcloud services enable discoveryengine.googleapis.com
```

### Model Not Available

Some models may not be available in all regions. Try:
1. Switch to `us-central1` region
2. Use stable model names (without preview)
3. Check [Vertex AI documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models) for availability

### Quota Issues

If you hit quota limits:
1. Check your [Google Cloud Console quotas](https://console.cloud.google.com/iam-admin/quotas)
2. Request quota increases if needed
3. Consider using `gemini-2.5-flash` for lower resource usage

## 🎉 Next Steps

Once setup is complete:

1. **Run DeerFlow**: `uv run main.py "Your research question"`
2. **Use Web UI**: `./bootstrap.sh -d` then visit `http://localhost:3000`
3. **Explore Examples**: Check the `examples/` directory for sample research reports

## 📚 Additional Resources

- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [Gemini Models Overview](https://ai.google.dev/gemini-api/docs/models)
- [LiteLLM Vertex AI Provider](https://docs.litellm.ai/docs/providers/vertex)
- [DeerFlow Configuration Guide](./configuration_guide.md)

---

**Need help?** Check the [FAQ](./FAQ.md) or create an issue in the repository.
